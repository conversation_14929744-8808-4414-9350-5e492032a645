.image-generator {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

#prompt-input {
  width: 100%;
  max-width: 600px;
  padding: 10px;
  margin-bottom: 20px;
  font-size: 16px;
}

#generate-btn {
  padding: 10px 20px;
  background-color: #4CAF50;
  color: white;
  border: none;
  cursor: pointer;
  font-size: 16px;
  margin-left: 10px;
}

#generate-btn:hover {
  background-color: #45a049;
}

#generate-btn.loading {
  background-color: #cccccc;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.image-container {
  position: relative;
}

.image-container img {
  width: 100%;
  height: auto;
  border-radius: 8px;
}

.download-btn {
  display: block;
  margin-top: 10px;
  padding: 8px 12px;
  background-color: #2196F3;
  color: white;
  text-align: center;
  text-decoration: none;
  border-radius: 4px;
}

.download-btn:hover {
  background-color: #0b7dda;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
}