import React, { useState } from "react";
import "./App.css";
import axios from "axios";

const JokeApp = () => {
  const [joke, setJoke] = useState("");
  const [loading, setLoading] = useState(false);

  // Fetch joke from the API using Axios
  const fetchJoke = async () => {
    setLoading(true);
    try {
      const response = await axios.get(
        "https://official-joke-api.appspot.com/random_joke"
      );
      setJoke(response.data.setup + " - " + response.data.punchline);
    } catch (error) {
      console.error("Error fetching joke:", error);
      setJoke("Sorry, we couldn’t fetch a joke at the moment.");
    }
    setLoading(false);
  };

  return (
    <div className="App">
      <h1>Random Joke Generator</h1>
      <button onClick={fetchJoke} disabled={loading}>
        {loading ? "Loading..." : "Get a Joke!"}
      </button>
      <p className="joke">{joke}</p>
    </div>
  );
};

export default JokeApp;
