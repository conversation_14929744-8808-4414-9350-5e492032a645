import React, { useState, useEffect } from "react";

const ImageGenerator = () => {
  const [images, setImages] = useState([
    "images/Img-1.jpeg",
    "images/Img-2.jpeg",
    "images/Img-3.jpeg",
    "images/Img-4.jpeg",
    "images/Img-5.jpeg",
    "images/Img-6.jpeg",
    "images/Img-7.jpeg",
    "images/Img-8.jpeg",
    "images/Img-9.jpeg",
    "images/Img-10.jpeg",
    "images/Img-11.jpeg",
    "images/Img-12.jpeg",
  ]);
  const [displayedImages, setDisplayedImages] = useState([]);
  const [prompt, setPrompt] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [placeholder, setPlaceholder] = useState("");

  // const token = "*************************************";
  const token = "*************************************";
  const imageIds = ["img1", "img2", "img3", "img4"];

  useEffect(() => {
    // Shuffle images on initial load
    const shuffled = [...images].sort(() => Math.random() - 0.5);
    setDisplayedImages(shuffled.slice(0, 4));
    updatePlaceholder();

    window.addEventListener("resize", updatePlaceholder);
    return () => window.removeEventListener("resize", updatePlaceholder);
  }, []);

  const updatePlaceholder = () => {
    if (window.innerWidth < 375) {
      setPlaceholder("Enter details");
    } else if (window.innerWidth >= 375 && window.innerWidth < 768) {
      setPlaceholder("Describe Image");
    } else {
      setPlaceholder(
        'Try Something like "Panda playing cricket in white clothes"'
      );
    }
  };

  const query = async (promptText) => {
    const data = { inputs: promptText };
    const response = await fetch(
      "https://api-inference.huggingface.co/models/stabilityai/stable-diffusion-xl-base-1.0",
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }
    );

    if (!response.ok) {
      const message = `An error has occurred: ${response.status}`;
      throw new Error(message);
    }

    return await response.blob();
  };

  const handleGenerate = async (e) => {
    e.preventDefault();
    if (prompt.trim() === "") {
      alert("Please enter some text to generate images.");
      return;
    }

    setIsLoading(true);

    try {
      const imagePromises = imageIds.map(() => query(prompt));
      const responses = await Promise.all(imagePromises);

      const newImages = responses.map((response, index) => {
        const objectURL = URL.createObjectURL(response);
        return {
          src: objectURL,
          downloadUrl: objectURL,
          filename: `Generated_Img_${index + 1}.jpg`,
        };
      });

      setDisplayedImages(newImages);
    } catch (error) {
      console.error("Failed to fetch images:", error);
      alert("Failed to generate images. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      handleGenerate(e);
    }
  };

  return (
    <div className="image-generator">
      <form onSubmit={handleGenerate}>
        <input
          type="text"
          id="prompt-input"
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
        />
        <button
          id="generate-btn"
          type="submit"
          className={isLoading ? "loading" : ""}
        >
          Generate
        </button>
      </form>

      <div className="image-grid">
        {displayedImages.map((img, index) => (
          <div key={index} className="image-container">
            {isLoading && <div className="loading-overlay">Loading...</div>}
            <img
              id={`img${index + 1}`}
              src={img.src || images[index]}
              alt={`Generated ${index + 1}`}
            />
            <a
              href={img.downloadUrl || images[index]}
              download={img.filename || `Image_${index + 1}.jpg`}
              className="download-btn"
            >
              Download
            </a>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ImageGenerator;
