import React, { useState, useEffect } from 'react';
// import './styles.css';

const Stopwatch = () => {
  const [time, setTime] = useState(0); // Time in milliseconds
  const [isRunning, setIsRunning] = useState(false);

  useEffect(() => {
    let interval;
    if (isRunning) {
      interval = setInterval(() => {
        setTime((prevTime) => prevTime + 1000);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isRunning]);

  const startStop = () => {
    setIsRunning(!isRunning);
  };

  const reset = () => {
    setIsRunning(false);
    setTime(0);
  };

  // Display time in seconds with two decimal places
  //const displayTime = () => (time / 1000).toFixed(2);

  return (
    <div className="stopwatch">
      <center>

        <h1>Stopwatch</h1>
        <div className="time">{time / 1000} seconds</div>
        <div className="buttons">
          <button onClick={startStop}>{isRunning ? "Stop" : "Start"}</button>
          <button onClick={reset}>Reset</button>
        </div>
      </center>
    </div>
  );
};

export default Stopwatch;