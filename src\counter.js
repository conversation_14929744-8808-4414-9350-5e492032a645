import { useState, useEffect } from "react";

import "./App.css";

const Counter = () => {
  const [count, setCount] = useState(0);
  const [num, setNum] = useState(10);

  useEffect(() => {
    setCount((c) => c + 1);
    setNum((n) => n + 1);
  }, []);

  return (
    <div className="App">
      <h1>Counter is {count}</h1>
      <hr color="red" />
      <h2>Num is {num}!</h2>
    </div>
  );
};

export default Counter;
