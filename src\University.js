// src/components/UniversitiesList.js
import React, { useEffect, useState } from "react";
import axios from "axios";

const UniversitiesList = () => {
  const [country, setCountry] = useState("");
  const [universities, setUniversities] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const fetchUniversities = async () => {
    if (!country.trim()) return;

    setLoading(true);
    setError("");
    try {
      const response = await axios.get(
        `http://universities.hipolabs.com/search?country=${country}`
      );
      console.log("response data is ",response.data);
    //   const obj=await response.json();
    //   console.log("json format",obj);
       setUniversities(response.data);
    } catch (err) {
      setError("Failed to fetch data. Please try again.");
    } finally {
      setLoading(false);
    }
  };


  /*
  useEffect(()=>{
    fetchUniversities();
  },[]);
*/

useEffect(() => {
  if (country.trim() === "") {
    setUniversities([]);
    return;
  }

  const delayDebounce = setTimeout(() => {
    fetchUniversities();
  }, 500); // wait for 500ms after user stops typing

  return () => clearTimeout(delayDebounce);
}, [country]);



  return (
    <div className="p-4 max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold mb-4 text-center">University Finder</h2>
      <div className="flex gap-2 mb-4">
        <input
          type="text"
          placeholder="Enter country (e.g. India)"
          value={country}
          onChange={(e) => setCountry(e.target.value)}
          className="flex-1 border border-gray-300 px-3 py-2 rounded"
        />
        <button
          onClick={fetchUniversities}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          Search
        </button>
      </div>

      {loading && <p>Loading...</p>}
      {error && <p className="text-red-600">{error}</p>}
      <h1>total universites present are {universities.length}</h1>
      <ol className="space-y-2" type="1">
        {universities.map((uni, index) => (
          <li key={index} className="border p-2 rounded shadow-sm">
            <strong>{uni.name}</strong> <br />
            <a
              href={uni.web_pages[0]}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-500 underline"
            >
              Visit Website
            </a>
          </li>
        ))}
      </ol>
    </div>
  );
};

export default UniversitiesList;
