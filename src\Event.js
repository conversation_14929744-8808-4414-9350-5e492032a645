import React from "react";

const Event = () => {
  const handleClick = (level) => (e) => {
    alert(`Clicked on ${level} box`);
    // e.stopPropagation(); // Uncomment to stop bubbling here
  };

  return (
    <div
      onClick={handleClick("Outer")}
      style={{
        padding: "40px",
        backgroundColor: "red",
      }}
    >
      Outer Box
      <div
        onClick={handleClick("Middle")}
        style={{
          padding: "30px",
          backgroundColor: "green",
        }}
      >
        Middle Box
        <div
          onClick={handleClick("Inner")}
          style={{
            padding: "20px",
            backgroundColor: "yellow",
            color: "yellow",
          }}
        >
          Inner Box (Click Me)
        </div>
      </div>
    </div>
  );
};

export default Event;
