import React, { useState, useEffect } from "react";
import axios from "axios";

const Pokemontest = () => {
  const [pokemon, setPokemon] = useState([]);
  const [pokemonName, setPokemonName] = useState([]);
  const [pokemonImage, setPokemonImage] = useState([]);
  const [pokemonData, setPokemonData] = useState([]);

  async function getPokemon() {
    const res = await axios.get("https://pokeapi.co/api/v2/pokemon?limit=10");
    console.log("Results are", res);
    setPokemon(res.data.results);

    pokemon.map(async (pokemon)=>{
        const response=await axios.get(pokemon.url);
        setPokemonData((prev)=>[...prev,response.data]);
    });
    const response=await axios.get(res.data.results[0].url);
    console.log("url call response is",response);
    console.log("image is",response.data.sprites.back_default);
    //setPokemonData(response.data);
  }

  useEffect(() => {
    getPokemon(); // run only once on mount
  }, []); // empty dependency array


   for (let i = 0; i < pokemon.length; i++) {
  //   console.log("index is", i + 1, "pokemon name is", pokemon[i].name);
      setPokemonName((prev)=>[...prev,pokemon[i].name]);
   }

  //console.log("api format",)
  return <></>;
};

export default Pokemontest;
