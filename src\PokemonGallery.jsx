import React, { useState, useEffect } from 'react';
import axios from "axios";

const PokemonGallery = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [allPokemonData, setAllPokemonData] = useState([]);
  const [filteredPokemon, setFilteredPokemon] = useState([]);
  const [loading, setLoading] = useState(true);

  // Fetch all Pokémon data on initial load
  useEffect(() => {
    const fetchAllPokemon = async () => {
      try {
        setLoading(true);

        // Step 1: Get list of names and URLs
        const res = await axios.get('https://pokeapi.co/api/v2/pokemon?limit=100');
        const results = res.data.results; // { name, url }

        // Step 2: Fetch details (images) for each Pokémon
        const detailsPromises = results.map(p => axios.get(p.url));
        const detailsResponses = await Promise.all(detailsPromises);

        const fullData = detailsResponses.map(res => ({
          name: res.data.name,
          image: res.data.sprites.front_default
        }));

        setAllPokemonData(fullData);
        setFilteredPokemon(fullData);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching Pokémon:', error);
        setLoading(false);
      }
    };

    fetchAllPokemon();
  }, []);

  // Handle input filter
  const handleSearch = (e) => {
    const value = e.target.value.toLowerCase();
    setSearchTerm(value);

    const filtered = allPokemonData.filter(p =>
      p.name.startsWith(value)
    );

    setFilteredPokemon(filtered);
  };

  return (
    <div style={{ textAlign: 'center', marginTop: '2rem', padding: '0 20px' }}>
      <h1>All Pokémon Gallery</h1>

      {/* Search Bar */}
      <input
        type="text"
        placeholder="Search Pokémon..."
        value={searchTerm}
        onChange={handleSearch}
        style={{
          padding: '10px',
          fontSize: '16px',
          width: '250px',
          marginBottom: '20px'
        }}
      />

      {/* Loading Spinner */}
      {loading && <div style={{ fontSize: '18px', margin: '20px' }}>🔄 Loading Pokémon...</div>}

      {/* Pokémon Cards */}
      <div style={{
        display: 'flex',
        flexWrap: 'wrap',
        justifyContent: 'center',
        gap: '20px',
        marginTop: '2rem'
      }}>
        {!loading && filteredPokemon.map( (pokemon,index) => (
          <div
            key={pokemon.name}
            style={{
              width: '140px',
              padding: '15px',
              boxShadow: '0 4px 8px grey',
              border: '1.5px dotted grey',
              borderRadius: '20px',
              textAlign: 'center',
              backgroundColor: '#f8f8f8',
              transition: 'transform 0.2s',
              cursor: 'pointer'
            }}  
          >
            <img
              src={pokemon.image}
              alt={pokemon.name}
              style={{ height: '100px', width: '100px', marginBottom: '10px' }}
            />
            <p style={{ margin: 0, fontWeight: 'bold' }}> {index+1}  {pokemon.name}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PokemonGallery;

