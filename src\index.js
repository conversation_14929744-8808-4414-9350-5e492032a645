import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';
import reportWebVitals from './reportWebVitals';
import ImageGenerator from './ImageGenerator';
import PokemonGallery from './PokemonGallery';
import Country from './Country';
import Stopwatch from './Stopwatch';
import Event from './Event';
import UniversitiesList from "./University.js"
import Pokemontest from "./Pokemontest.jsx";
import JokeApp from "./jokes.js";
import {Loader} from "./Loader.js"
import Counter from "./counter.js";
const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    {/* <JokeApp /> */}
    {/* <App /> */}
    {/* <ImageGenerator /> */}
    <Pokemontest/>
    {/* <PokemonGallery /> */}
    {/* <Country/>
    <Stopwatch/> */}
    {/* <Event /> */}
    {/* <Counter></Counter>
    <Loader />
    <UniversitiesList /> */}
  </React.StrictMode>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
