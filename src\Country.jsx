import React, { useEffect, useState } from "react";
import axios from "axios";

const Country = () => {
  const [countries, setCountries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    axios
      .get("https://restcountries.com/v3.1/all")
      .then((response) => {
        setCountries(response.data);
        setLoading(false);
      })
      .catch((error) => {
        setError(error.message);
        setLoading(false);
      });
  }, []);

  if (loading) return <p>Loading...</p>;
  if (error) return <p>Error: {error}</p>;

  return (
    <div>
      <h1>Country Flags</h1>
      <ul>
        {countries.map((country) => (
          <li key={country.cca2}>
            <p><strong>{country.name.common}</strong></p>
            <img src={country.flags.png} alt={country.name.common} width="25%" height="10%"/>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default Country;

